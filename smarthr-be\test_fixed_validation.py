#!/usr/bin/env python3
"""
Test the fixed validation function with the user's problematic responses.
"""

from controllers.interview_controller import _validate_evaluation_result
from models.interview import EvaluationResult, QuestionEvaluation, Seniority

def test_fixed_validation():
    print("Testing fixed validation function...")
    
    # Test case 1: User's problematic example with garbled responses
    print("\n1. Testing user's problematic example...")
    
    questions_and_responses = [
        ("Power Platform experience", "I don´t know"),  # Garbled
        ("Power Platform updates", "I don´t know"),     # Garbled
        ("Azure experience", "I don´t know"),           # Garbled
        ("JavaScript experience", "I don´t know"),      # Garbled
        ("C# experience", "I don´t know"),              # Garbled
        ("SQL experience", "I don´t know"),             # Garbled
        ("Power BI experience", "I could¿ve know"),     # Garbled
        ("Custom connectors", "I don´t know"),          # Garbled
        ("Power Pages", "I don´t know"),                # Garbled
        ("PL-400/PL-600 cert", "I don´t know")          # Garbled
    ]
    
    per_question_evals = []
    for i, (question, response) in enumerate(questions_and_responses, 1):
        if "could¿ve" in response:
            explanation = f"Candidate responded '{response}' - incomplete/garbled response"
        else:
            explanation = f"Candidate responded '{response}' - garbled text with encoding issues"
        
        per_question_evals.append(QuestionEvaluation(
            question_number=i,
            expected_seniority=Seniority.SENIOR,
            detected_seniority=Seniority.JUNIOR,
            explanation=explanation
        ))
    
    # This should NOT be 100% anymore
    result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=per_question_evals,
        percentage_of_match=100.0,  # This should be flagged as problematic
        explanation="Mostly garbled responses"
    )
    
    issues = _validate_evaluation_result(result, len(questions_and_responses), "test")
    
    print(f"   Found {len(issues)} validation issues:")
    for issue in issues:
        print(f"   - {issue}")
    
    if len(issues) > 0:
        print("   ✅ FIXED: Validation now catches the 100% scoring bug!")
    else:
        print("   ❌ Still broken: Validation didn't catch the issue")
    
    # Test case 2: Honest admissions (should be valid)
    print("\n2. Testing honest admissions (should be valid)...")
    
    honest_result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=[
            QuestionEvaluation(
                question_number=1,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I don't know' - honest admission of lack of knowledge"
            ),
            QuestionEvaluation(
                question_number=2,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I'm not sure' - honest admission showing limited experience"
            )
        ],
        percentage_of_match=100.0,  # Should be valid for honest admissions
        explanation="Honest admissions"
    )
    
    honest_issues = _validate_evaluation_result(honest_result, 2, "test")
    
    print(f"   Found {len(honest_issues)} validation issues:")
    for issue in honest_issues:
        print(f"   - {issue}")
    
    if len(honest_issues) == 0:
        print("   ✅ CORRECT: Honest admissions are properly handled")
    else:
        print("   ❌ Issue: Honest admissions are being flagged incorrectly")
    
    # Test case 3: Mixed scenario
    print("\n3. Testing mixed scenario...")
    
    mixed_result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=[
            QuestionEvaluation(
                question_number=1,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I don't know' - honest admission of lack of knowledge"
            ),
            QuestionEvaluation(
                question_number=2,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate responded 'I could¿ve know' - incomplete/garbled response"
            ),
            QuestionEvaluation(
                question_number=3,
                expected_seniority=Seniority.MID,
                detected_seniority=Seniority.MID,
                explanation="Candidate provided good technical explanation"
            )
        ],
        percentage_of_match=66.7,  # Should be ~67% - 2 adequate out of 3
        explanation="Mixed response quality"
    )
    
    mixed_issues = _validate_evaluation_result(mixed_result, 3, "test")
    
    print(f"   Found {len(mixed_issues)} validation issues:")
    for issue in mixed_issues:
        print(f"   - {issue}")
    
    if len(mixed_issues) == 0:
        print("   ✅ CORRECT: Mixed scenario handled properly")
    else:
        print("   ⚠️  Note: Some validation issues detected (may be expected)")

if __name__ == "__main__":
    test_fixed_validation()
