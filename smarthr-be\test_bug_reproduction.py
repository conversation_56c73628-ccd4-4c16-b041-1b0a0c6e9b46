#!/usr/bin/env python3
"""
Simple test to reproduce the 100% scoring bug.
"""

from controllers.interview_controller import _validate_evaluation_result
from models.interview import EvaluationResult, QuestionEvaluation, Seniority

def test_bug_reproduction():
    print("Testing 100% scoring bug reproduction...")
    
    # Create the problematic case from user's example
    result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=[
            QuestionEvaluation(
                question_number=1,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate responded 'I could¿ve know' - incomplete/garbled response"
            ),
            QuestionEvaluation(
                question_number=2,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I don´t know' - brief response"
            )
        ],
        percentage_of_match=100.0,  # This is the bug
        explanation="Junior level responses"
    )
    
    # Test validation
    issues = _validate_evaluation_result(result, 2, "test")
    
    print(f"Found {len(issues)} validation issues:")
    for issue in issues:
        print(f"  - {issue}")
    
    if len(issues) == 0:
        print("BUG CONFIRMED: No validation issues found for 100% score with poor responses")
        return False
    else:
        print("Validation working: Issues detected")
        return True

if __name__ == "__main__":
    test_bug_reproduction()
